{"name": "bali_blissed", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^7.0.1", "@fortawesome/free-brands-svg-icons": "^7.0.1", "@fortawesome/free-regular-svg-icons": "^7.0.1", "@fortawesome/free-solid-svg-icons": "^7.0.1", "@fortawesome/react-fontawesome": "^3.0.2", "@genkit-ai/googleai": "^1.19.2", "@hookform/resolvers": "^5.2.2", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.15", "@radix-ui/react-tooltip": "^1.2.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.2", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.13", "genkit": "^1.19.2", "lucide-react": "^0.544.0", "next": "15.5.3", "next-themes": "^0.4.6", "nodemailer": "^7.0.6", "opentelemetry": "^0.1.0", "react": "19.1.0", "react-day-picker": "^9.10.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "recharts": "^3.2.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "three": "^0.180.0", "zod": "^3.25.76", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9.35.0", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.15", "@types/nodemailer": "^7.0.1", "@types/react": "^19.1.13", "@types/react-dom": "^19", "@types/three": "^0.180.0", "eslint": "^9.35.0", "eslint-config-next": "^15.5.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-refresh": "^0.4.20", "genkit-cli": "^1.19.2", "globals": "^16.4.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^4", "typescript": "^5.9.2", "typescript-eslint": "^8.44.0"}}